'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Card, Steps, Button, Space, message, Spin } from 'antd';
import { 
  FileTextOutlined, 
  SettingOutlined, 
  ToolOutlined, 
  AppstoreOutlined,
  DollarOutlined,
  CheckCircleOutlined 
} from '@ant-design/icons';

// 导入管理后台的算价组件和类型
import { 
  CalculationState, 
  CalculationStep,
  BoxBasicInfo,
  PackagingConfig,
  MaterialConfig,
  ProcessConfig,
  AccessoryConfig,
  ProcessingFeeConfig,
  FormulaConfig
} from '@/app/admin/box/calculation/types/calculation';
import { useCalculationState } from '@/app/admin/box/calculation/hooks/useCalculationState';
import { calculationEngineApi } from '@/services/calculationApi';

// 导入步骤组件（需要适配为用户端版本）
import UserBasicInfoStep from './steps/UserBasicInfoStep';
import UserPackagingStep from './steps/UserPackagingStep';
import UserProcessStep from './steps/UserProcessStep';
import UserAccessoryStep from './steps/UserAccessoryStep';
import UserProcessingFeeStep from './steps/UserProcessingFeeStep';
import UserQuotationStep from './steps/UserQuotationStep';
import DebugPanel from './DebugPanel';

import { Box } from '@/types/box';
import { CurrentUser } from '@/types/user';
import { QuotationDetail } from '@/app/admin/box/calculation/types/pricing';

interface UserCalculationWizardProps {
  sourceBox?: Box | null;
  user?: CurrentUser | null;
}

/**
 * 用户端算价向导组件
 * 复用管理后台算价逻辑，适配前端设计风格
 */
export default function UserCalculationWizard({ sourceBox, user }: UserCalculationWizardProps) {
  const {
    state,
    updateBasicInfo,
    updatePartConfig,
    updatePackagingConfig,
    updateMaterialConfig,
    updateProcessConfig,
    updateAccessoryConfig,
    updateProcessingFeeConfig,
    updateFormulaConfig,
    updateQuotation,
    setCurrentStep,
    resetState
  } = useCalculationState();

  const [isCalculating, setIsCalculating] = useState(false);
  const [quotation, setQuotation] = useState<QuotationDetail | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // 确保状态完全初始化
  useEffect(() => {
    if (state && state.basicInfo) {
      setIsInitialized(true);
    }
  }, [state]);

  // 步骤配置
  const steps = [
    {
      title: '基础信息',
      icon: <FileTextOutlined />,
      description: '选择盒型和填写基本参数',
      key: CalculationStep.BASIC_INFO
    },
    {
      title: '材料选择',
      icon: <SettingOutlined />,
      description: '选择材料和拼版配置',
      key: CalculationStep.PACKAGING
    },
    {
      title: '工艺选择',
      icon: <ToolOutlined />,
      description: '选择印刷和后道工艺',
      key: CalculationStep.PROCESS
    },
    {
      title: '配件选择',
      icon: <AppstoreOutlined />,
      description: '选择包装配件',
      key: CalculationStep.ACCESSORY
    },
    {
      title: '加工费用',
      icon: <DollarOutlined />,
      description: '选择加工费用项目',
      key: CalculationStep.PROCESSING_FEE
    },
    {
      title: '确认报价',
      icon: <CheckCircleOutlined />,
      description: '查看详细报价单',
      key: CalculationStep.QUOTATION
    }
  ];

  // 获取当前步骤索引
  const getCurrentStepIndex = () => {
    return steps.findIndex(step => step.key === state.currentStep);
  };

  // 自动重新计算报价
  const recalculateQuotation = useCallback(async () => {
    try {
      setIsCalculating(true);

      // 检查状态是否完整
      console.log('当前计算状态:', state);

      if (!state || !state.basicInfo) {
        console.warn('计算状态不完整，跳过报价计算', { state });
        message.warning('请先完善基础信息');
        return;
      }

      // 确保基础信息完整
      if (!state.basicInfo.quantity || state.basicInfo.quantity <= 0) {
        console.warn('数量信息无效:', state.basicInfo.quantity);
        message.warning('请先填写有效的数量信息');
        return;
      }

      // 调用用户端的计算引擎API
      const result = await calculationEngineApi.generateQuotation(state);
      if (result.success && result.data) {
        setQuotation(result.data);
        updateQuotation(result.data);
      } else {
        message.error(result.error?.message || '计算失败');
      }
    } catch (error) {
      console.error('计算错误:', error);
      message.error('计算失败，请检查配置');
    } finally {
      setIsCalculating(false);
    }
  }, [state, updateQuotation]);

  // 下一步
  const handleNext = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1].key);
    }
  };

  // 上一步
  const handlePrev = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1].key);
    }
  };

  // 重置所有数据
  const handleReset = () => {
    resetState();
    setQuotation(null);
    message.info('已重置所有数据');
  };

  // 渲染当前步骤内容
  const renderStepContent = () => {
    const updateHandlers = {
      basicInfo: updateBasicInfo,
      partConfig: updatePartConfig,
      packagingConfig: updatePackagingConfig,
      materialConfig: updateMaterialConfig,
      processConfig: updateProcessConfig,
      accessoryConfig: updateAccessoryConfig,
      processingFeeConfig: updateProcessingFeeConfig,
      formulaConfig: updateFormulaConfig
    };

    switch (state.currentStep) {
      case CalculationStep.BASIC_INFO:
        return (
          <UserBasicInfoStep
            state={state}
            onUpdate={updateHandlers}
            onRecalculate={recalculateQuotation}
            sourceBox={sourceBox}
          />
        );
      case CalculationStep.PACKAGING:
        return (
          <UserPackagingStep
            state={state}
            onUpdate={updateHandlers}
            onRecalculate={recalculateQuotation}
          />
        );
      case CalculationStep.PROCESS:
        return (
          <UserProcessStep
            state={state}
            onUpdate={updateHandlers}
            onRecalculate={recalculateQuotation}
          />
        );
      case CalculationStep.ACCESSORY:
        return (
          <UserAccessoryStep
            state={state}
            onUpdate={updateHandlers}
            onRecalculate={recalculateQuotation}
          />
        );
      case CalculationStep.PROCESSING_FEE:
        return (
          <UserProcessingFeeStep
            state={state}
            onUpdate={updateHandlers}
            onRecalculate={recalculateQuotation}
          />
        );
      case CalculationStep.QUOTATION:
        return (
          <UserQuotationStep
            state={state}
            quotation={quotation}
            user={user}
            onRecalculate={recalculateQuotation}
          />
        );
      default:
        return null;
    }
  };

  // 如果状态还没有初始化，显示加载状态
  if (!isInitialized) {
    return (
      <Card>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '400px'
        }}>
          <Spin size="large" />
          <span style={{ marginLeft: 16 }}>正在初始化算价系统...</span>
        </div>
      </Card>
    );
  }

  return (
    <div className="user-calculation-wizard">
      {/* 步骤导航 - 响应式设计 */}
      <Card style={{ marginBottom: 24 }}>
        <Steps
          current={getCurrentStepIndex()}
          direction="horizontal"
          size="small"
          items={steps.map(step => ({
            title: step.title,
            description: window.innerWidth > 768 ? step.description : undefined,
            icon: step.icon
          }))}
          style={{ marginBottom: 0 }}
          responsive={false}
        />
      </Card>

      {/* 步骤内容 */}
      <div style={{ minHeight: '600px' }}>
        {isCalculating ? (
          <Card>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center', 
              minHeight: '400px' 
            }}>
              <Spin size="large" />
              <span style={{ marginLeft: 16 }}>正在计算报价...</span>
            </div>
          </Card>
        ) : (
          renderStepContent()
        )}
      </div>

      {/* 操作按钮 - 响应式设计 */}
      <Card style={{ marginTop: 24 }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '16px'
        }}>
          <Button onClick={handleReset} style={{ order: window.innerWidth <= 768 ? 2 : 1 }}>
            重新开始
          </Button>

          <Space style={{ order: window.innerWidth <= 768 ? 1 : 2 }}>
            {getCurrentStepIndex() > 0 && (
              <Button onClick={handlePrev}>
                上一步
              </Button>
            )}
            {getCurrentStepIndex() < steps.length - 1 && (
              <Button type="primary" onClick={handleNext}>
                下一步
              </Button>
            )}
          </Space>
        </div>
      </Card>

      {/* 调试面板 - 仅开发环境 */}
      <DebugPanel
        state={state}
        quotation={quotation}
        onTestCalculation={recalculateQuotation}
      />
    </div>
  );
}
