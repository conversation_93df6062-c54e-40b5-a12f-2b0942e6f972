import { NextRequest } from 'next/server';
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';

/**
 * 用户端生成报价接口
 * 简化版的报价计算，复用管理后台的计算逻辑
 */
const handler = async (request: AuthenticatedRequest) => {
  try {
    const body = await request.json();
    const { state } = body;

    // 这里应该实现实际的计算逻辑
    // 目前返回一个模拟的报价结果
    const mockQuotation = {
      id: Date.now(),
      projectName: state.basicInfo?.name || '未命名项目',
      quantity: state.basicInfo?.quantity || 1000,
      materialCost: 1200.50,
      processCost: 800.30,
      accessoryCost: 300.20,
      processingFeeCost: 150.00,
      formulaCost: 0,
      subtotal: 2451.00,
      taxRate: 0.13,
      taxAmount: 318.63,
      finalTotal: 2769.63,
      currency: 'CNY',
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天有效期
      createdAt: new Date().toISOString(),
      costGroups: [
        {
          name: '材料费用',
          items: [
            { name: '面纸', quantity: 1000, unit: '张', unitPrice: 0.8, totalPrice: 800 },
            { name: '灰板', quantity: 1000, unit: '张', unitPrice: 0.4, totalPrice: 400.50 }
          ],
          subtotal: 1200.50
        },
        {
          name: '工艺费用',
          items: [
            { name: '印刷', quantity: 1000, unit: '张', unitPrice: 0.5, totalPrice: 500 },
            { name: '覆膜', quantity: 1000, unit: '张', unitPrice: 0.3, totalPrice: 300.30 }
          ],
          subtotal: 800.30
        },
        {
          name: '配件费用',
          items: [
            { name: '手提袋', quantity: 100, unit: '个', unitPrice: 3.0, totalPrice: 300.20 }
          ],
          subtotal: 300.20
        },
        {
          name: '加工费用',
          items: [
            { name: '开槽费', quantity: 1, unit: '项', unitPrice: 150, totalPrice: 150 }
          ],
          subtotal: 150.00
        }
      ]
    };

    return successResponse(mockQuotation, '报价生成成功');
  } catch (error) {
    console.error('生成报价失败:', error);
    return errorResponse(ErrorCode.INTERNAL_ERROR, '生成报价失败', null, 500);
  }
};

// 用户端接口：需要登录但不需要管理员权限
export const POST = withAuth(handler, { requireAuth: true });
