'use client';

import React, { useState, useEffect } from 'react';
import { Card, Select, Form, Input, InputNumber, Row, Col, Button, Space, message, Spin } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { CalculationState } from '@/app/admin/box/calculation/types/calculation';
import { Box } from '@/types/box';
import { calculationBoxApi } from '@/services/calculationApi';

// 复用管理后台的基础信息步骤组件，但添加用户端的API调用
import BasicInfoStep from '@/app/admin/box/calculation/components/steps/BasicInfoStep';
import UserBoxSelector from '../UserBoxSelector';

interface UserBasicInfoStepProps {
  state: CalculationState;
  onUpdate: {
    basicInfo: (data: any) => void;
    partConfig: (data: any) => void;
    packagingConfig: (data: any) => void;
    materialConfig: (data: any) => void;
    processConfig: (data: any) => void;
    accessoryConfig: (data: any) => void;
    processingFeeConfig: (data: any) => void;
    formulaConfig: (data: any) => void;
  };
  onRecalculate: () => void;
  sourceBox?: Box | null;
}

/**
 * 用户端基础信息步骤组件
 * 复用管理后台组件，添加用户端样式适配
 */
export default function UserBasicInfoStep({
  state,
  onUpdate,
  onRecalculate,
  sourceBox
}: UserBasicInfoStepProps) {
  const [selectedBox, setSelectedBox] = useState<Box | null>(sourceBox || null);

  // 处理盒型选择
  const handleBoxSelect = (box: Box) => {
    setSelectedBox(box);

    // 自动填充基础信息
    const calculationName = `${box.name}_计算_${new Date().toLocaleString('zh-CN', { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' })}`;

    // 处理动态属性，填充默认值
    const dynamicAttributes = (box.attributes || []).map(attr => ({
      ...attr,
      value: attr.value || 0
    }));

    const updatedBasicInfo = {
      name: calculationName,
      quantity: 1000, // 默认数量
      attributes: dynamicAttributes,
      parts: box.parts
    };

    onUpdate.basicInfo(updatedBasicInfo);
    message.success('盒型信息已自动填充，请检查并调整参数');
  };

  return (
    <div className="user-basic-info-step">
      {/* 用户端提示信息 */}
      <Card style={{ marginBottom: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}>
        <div style={{ color: '#52c41a' }}>
          <h4 style={{ margin: 0, color: '#52c41a' }}>💡 操作提示</h4>
          <p style={{ margin: '8px 0 0 0', color: '#52c41a' }}>
            请选择您需要的盒型模板，并填写相关参数。如果您不确定参数值，可以使用默认值进行计算。
          </p>
        </div>
      </Card>

      {/* 用户端盒型选择器 */}
      <UserBoxSelector
        selectedBox={selectedBox}
        onBoxSelect={handleBoxSelect}
      />

      {/* 复用管理后台的基础信息组件 */}
      <BasicInfoStep
        state={state}
        onUpdate={onUpdate}
        onRecalculate={onRecalculate}
        sourceBox={selectedBox}
      />
    </div>
  );
}
