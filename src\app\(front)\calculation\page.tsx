'use client';

import React, { useState, useEffect } from 'react';
import { Layout, ConfigProvider, Spin, message } from 'antd';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { Header, Footer } from '@/components/front';
import { Breadcrumb } from '@/components/shared';
import UserCalculationWizard from './components/UserCalculationWizard';
import { Box } from '@/types/box';
import { calculationBoxApi } from '@/services/calculationApi';

/**
 * 用户端算价页面
 * 复用管理后台算价功能，适配前端设计风格
 */
export default function UserCalculationPage() {
  const router = useRouter();
  const { user, loading: authLoading, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [sourceBox, setSourceBox] = useState<Box | null>(null);

  // 面包屑导航配置
  const breadcrumbItems = [
    { title: '首页', href: '/' },
    { title: '包装报价', href: '/quote' },
    { title: '在线算价' }
  ];

  // 检查用户认证状态
  useEffect(() => {
    if (!authLoading) {
      if (!isAuthenticated) {
        message.warning('请先登录后再使用算价功能');
        router.push(`/login?redirect=${encodeURIComponent('/calculation')}`);
        return;
      }
      setLoading(false);
    }
  }, [authLoading, isAuthenticated, router]);

  // 获取URL参数中的盒型ID（如果有）
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const boxId = urlParams.get('boxId');
    
    if (boxId && isAuthenticated) {
      loadBoxData(parseInt(boxId));
    }
  }, [isAuthenticated]);

  // 加载盒型数据
  const loadBoxData = async (boxId: number) => {
    try {
      setLoading(true);
      const result = await calculationBoxApi.getDetail(boxId);
      if (result.success && result.data) {
        setSourceBox(result.data);
      } else {
        message.error(result.error?.message || '加载盒型数据失败');
      }
    } catch (error) {
      console.error('加载盒型数据失败:', error);
      message.error('加载盒型数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 如果正在加载认证状态或页面数据，显示加载状态
  if (authLoading || loading) {
    return (
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: '#FF422D',
            colorPrimaryHover: '#FF6655',
            colorPrimaryActive: '#CC3B26',
            controlItemBgActive: '#FF422D',
          },
          components: {
            Menu: {
              itemSelectedBg: '#FF422D',
              itemSelectedColor: '#fff',
            },
          },
        }}
      >
        <Layout style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
          <Header />
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            minHeight: '400px' 
          }}>
            <Spin size="large" />
          </div>
          <Footer />
        </Layout>
      </ConfigProvider>
    );
  }

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#FF422D',
          colorPrimaryHover: '#FF6655',
          colorPrimaryActive: '#CC3B26',
          controlItemBgActive: '#FF422D',
        },
        components: {
          Menu: {
            itemSelectedBg: '#FF422D',
            itemSelectedColor: '#fff',
          },
        },
      }}
    >
      <Layout style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
        <Header />
        
        {/* 面包屑导航 */}
        <div className="bg-white px-6 py-4 mb-6 border-b border-gray-200">
          <div className="max-w-7xl mx-auto">
            <Breadcrumb items={breadcrumbItems} className="mb-2" />
            <h1 className="text-2xl font-bold text-gray-900">在线算价</h1>
            <p className="text-gray-600 mt-2">
              专业的包装盒算价工具，快速获取精准报价
            </p>
          </div>
        </div>

        {/* 主要内容 */}
        <div style={{ flex: 1, padding: '0 24px' }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <UserCalculationWizard
              sourceBox={sourceBox}
              user={user}
            />
          </div>
        </div>

        <Footer />
      </Layout>
    </ConfigProvider>
  );
}
