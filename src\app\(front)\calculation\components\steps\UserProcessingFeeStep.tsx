'use client';

import React from 'react';
import { Card } from 'antd';
import { CalculationState } from '@/app/admin/box/calculation/types/calculation';

// 直接复用管理后台的加工费步骤组件
import ProcessingFeeStep from '@/app/admin/box/calculation/components/steps/ProcessingFeeStep';

interface UserProcessingFeeStepProps {
  state: CalculationState;
  onUpdate: {
    basicInfo: (data: any) => void;
    partConfig: (data: any) => void;
    packagingConfig: (data: any) => void;
    materialConfig: (data: any) => void;
    processConfig: (data: any) => void;
    accessoryConfig: (data: any) => void;
    processingFeeConfig: (data: any) => void;
    formulaConfig: (data: any) => void;
  };
  onRecalculate: () => void;
}

/**
 * 用户端加工费选择步骤组件
 * 复用管理后台组件，添加用户端样式适配
 */
export default function UserProcessingFeeStep({
  state,
  onUpdate
}: UserProcessingFeeStepProps) {
  return (
    <div className="user-processing-fee-step">
      {/* 用户端提示信息 */}
      <Card style={{ marginBottom: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}>
        <div style={{ color: '#52c41a' }}>
          <h4 style={{ margin: 0, color: '#52c41a' }}>💡 操作提示</h4>
          <p style={{ margin: '8px 0 0 0', color: '#52c41a' }}>
            选择需要的加工费用项目，如开槽、喷码、检验等。这些费用会影响最终的总价。
          </p>
        </div>
      </Card>

      {/* 复用管理后台的加工费组件 */}
      <ProcessingFeeStep
        state={state}
        onUpdate={onUpdate}
      />
    </div>
  );
}
