'use client';

import React, { useState, useEffect } from 'react';
import { Card, Select, Row, Col, Button, Space, message, Spin, Image, Typography, Tag } from 'antd';
import { SearchOutlined, EyeOutlined } from '@ant-design/icons';
import { Box } from '@/types/box';
import { calculationBoxApi } from '@/services/calculationApi';

const { Option } = Select;
const { Text, Title } = Typography;

interface UserBoxSelectorProps {
  selectedBox?: Box | null;
  onBoxSelect: (box: Box) => void;
  loading?: boolean;
}

/**
 * 用户端盒型选择组件
 * 提供友好的盒型选择界面
 */
export default function UserBoxSelector({ selectedBox, onBoxSelect, loading = false }: UserBoxSelectorProps) {
  const [boxes, setBoxes] = useState<Box[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');

  // 加载盒型列表
  const loadBoxes = async (keyword?: string) => {
    try {
      setSearchLoading(true);
      const result = await calculationBoxApi.getList({
        page: 1,
        pageSize: 50,
        keyword: keyword || undefined
      });

      if (result.success && result.data) {
        setBoxes(result.data.list);
      } else {
        message.error(result.error?.message || '加载盒型列表失败');
      }
    } catch (error) {
      console.error('加载盒型列表失败:', error);
      message.error('加载盒型列表失败');
    } finally {
      setSearchLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadBoxes();
  }, []);

  // 搜索盒型
  const handleSearch = () => {
    loadBoxes(searchKeyword);
  };

  // 选择盒型
  const handleBoxSelect = async (boxId: number) => {
    try {
      const result = await calculationBoxApi.getDetail(boxId);
      if (result.success && result.data) {
        onBoxSelect(result.data);
        message.success(`已选择盒型：${result.data.name}`);
      } else {
        message.error(result.error?.message || '获取盒型详情失败');
      }
    } catch (error) {
      console.error('获取盒型详情失败:', error);
      message.error('获取盒型详情失败');
    }
  };

  return (
    <Card title="选择盒型模板" style={{ marginBottom: 16 }}>
      {/* 搜索区域 */}
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col flex="auto">
            <Select
              showSearch
              placeholder="搜索盒型名称..."
              style={{ width: '100%' }}
              loading={searchLoading}
              onSearch={setSearchKeyword}
              onSelect={handleBoxSelect}
              filterOption={false}
              notFoundContent={searchLoading ? <Spin size="small" /> : '暂无数据'}
            >
              {boxes.map(box => (
                <Option key={box.id} value={box.id}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <Text strong>{box.name}</Text>
                      {box.description && (
                        <div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {box.description}
                          </Text>
                        </div>
                      )}
                    </div>
                    <div>
                      <Tag color="blue">
                        {box._count?.parts || 0} 个部件
                      </Tag>
                    </div>
                  </div>
                </Option>
              ))}
            </Select>
          </Col>
          <Col>
            <Button 
              type="primary" 
              icon={<SearchOutlined />} 
              onClick={handleSearch}
              loading={searchLoading}
            >
              搜索
            </Button>
          </Col>
        </Row>
      </div>

      {/* 已选择的盒型信息 */}
      {selectedBox && (
        <Card 
          size="small" 
          title={
            <Space>
              <EyeOutlined />
              <span>已选择的盒型</span>
            </Space>
          }
          style={{ backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}
        >
          <Row gutter={[16, 8]}>
            <Col span={24}>
              <Title level={5} style={{ margin: 0, color: '#52c41a' }}>
                {selectedBox.name}
              </Title>
            </Col>
            {selectedBox.description && (
              <Col span={24}>
                <Text type="secondary">{selectedBox.description}</Text>
              </Col>
            )}
            <Col span={12}>
              <Text>
                <strong>部件数量：</strong>
                {selectedBox.parts?.length || 0} 个
              </Text>
            </Col>
            <Col span={12}>
              <Text>
                <strong>属性数量：</strong>
                {selectedBox.attributes?.length || 0} 个
              </Text>
            </Col>
            {selectedBox.processingFee && (
              <Col span={12}>
                <Text>
                  <strong>加工费：</strong>
                  ¥{selectedBox.processingFee}
                </Text>
              </Col>
            )}
            {selectedBox.processingBasePrice && (
              <Col span={12}>
                <Text>
                  <strong>起步价：</strong>
                  ¥{selectedBox.processingBasePrice}
                </Text>
              </Col>
            )}
          </Row>
        </Card>
      )}

      {/* 加载状态 */}
      {loading && (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 8 }}>
            <Text type="secondary">正在加载盒型信息...</Text>
          </div>
        </div>
      )}
    </Card>
  );
}
